import asyncio
import time
import pandas as pd
import os
import sys

# Add the YellowPage-scraper directory to the path
sys.path.append('YellowPage-scraper')
from scrapers.yp_scraper import all_business_urls, scrapeMe

# Business types that would be potential coffee customers
business_types = [
    "lawyers",
    "attorneys", 
    "law firms",
    "doctors",
    "medical offices",
    "dentists",
    "accounting firms",
    "accountants",
    "real estate offices",
    "insurance agencies",
    "banks",
    "financial services",
    "consulting firms",
    "engineering firms",
    "architecture firms",
    "marketing agencies",
    "advertising agencies",
    "technology companies",
    "software companies",
    "government offices",
    "non profit organizations",
    "corporate offices",
    "business services",
    "professional services"
]

async def scrape_business_type(business_type, location="Pittsburgh,PA"):
    """Scrape a specific business type in Pittsburgh, PA"""
    # Create Yellow Pages search URL
    search_terms = business_type.replace(" ", "+")
    location_terms = location.replace(" ", "+").replace(",", "%2C")
    url = f"https://www.yellowpages.com/search?search_terms={search_terms}&geo_location_terms={location_terms}"
    
    print(f"\n=== Scraping {business_type} in {location} ===")
    print(f"URL: {url}")
    
    try:
        # Get all business URLs for this category
        print("Getting business URLs...")
        bizz_urls = await all_business_urls(url)
        
        if not bizz_urls:
            print(f"No businesses found for {business_type}")
            return []
        
        print(f"Found {len(bizz_urls)} businesses. Scraping details...")
        
        # Scrape business details
        scrape_datas = await scrapeMe(bizz_urls)
        return scrape_datas
        
    except Exception as e:
        print(f"Error scraping {business_type}: {str(e)}")
        return []

async def main():
    """Main function to scrape all business types"""
    start_time = time.time()
    all_businesses = []
    
    print("Starting Pittsburgh Coffee Customer Business Scraper")
    print(f"Scraping {len(business_types)} business categories...")
    
    for i, business_type in enumerate(business_types, 1):
        print(f"\n[{i}/{len(business_types)}] Processing: {business_type}")
        
        try:
            businesses = await scrape_business_type(business_type)
            if businesses:
                # Add business category to each record
                for business in businesses:
                    business['Search_Category'] = business_type
                all_businesses.extend(businesses)
                print(f"Added {len(businesses)} businesses from {business_type}")
            
            # Small delay between categories to be respectful
            await asyncio.sleep(2)
            
        except Exception as e:
            print(f"Failed to process {business_type}: {str(e)}")
            continue
    
    # Save all results to a combined CSV file
    if all_businesses:
        print(f"\n=== SCRAPING COMPLETE ===")
        print(f"Total businesses found: {len(all_businesses)}")
        
        # Create DataFrame and save to CSV
        df = pd.DataFrame(all_businesses)
        
        # Create output directory if it doesn't exist
        os.makedirs('Pittsburgh_Coffee_Customers', exist_ok=True)
        
        # Save to CSV
        csv_filename = 'Pittsburgh_Coffee_Customers/pittsburgh_coffee_customers.csv'
        df.to_csv(csv_filename, index=False)
        print(f"Data saved to: {csv_filename}")
        
        # Also save to Excel
        excel_filename = 'Pittsburgh_Coffee_Customers/pittsburgh_coffee_customers.xlsx'
        df.to_excel(excel_filename, index=False)
        print(f"Data also saved to: {excel_filename}")
        
        # Print summary statistics
        print(f"\n=== SUMMARY ===")
        print(f"Total businesses: {len(df)}")
        print(f"Categories scraped: {df['Search_Category'].nunique()}")
        print(f"Businesses by category:")
        category_counts = df['Search_Category'].value_counts()
        for category, count in category_counts.items():
            print(f"  {category}: {count}")
            
    else:
        print("No businesses were successfully scraped.")
    
    total_time = round(time.time() - start_time, 2)
    time_in_mins = round(total_time / 60, 1)
    print(f"\nTotal time: {total_time} seconds ({time_in_mins} minutes)")

if __name__ == "__main__":
    asyncio.run(main())
